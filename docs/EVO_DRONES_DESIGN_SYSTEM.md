# 🚁 EVO DRONES - DESIGN SYSTEM COMPLETO

## 📋 **VISÃO GERAL**

Este documento define o sistema de design completo da plataforma **EVO DRONES**, estabelecendo padrões visuais, componentes, animações e diretrizes para manter consistência em todas as páginas e funcionalidades.

---

## 🎨 **IDENTIDADE VISUAL**

### **<PERSON><PERSON><PERSON> de Cores Principal**
```css
:root {
  /* Cores Primárias */
  --evo-primary: #30B4E8;        /* Azul ciano principal */
  --evo-primary-dark: #0E8BC0;   /* Azul escuro */
  --evo-primary-light: #7FD4F7;  /* Azul claro */
  
  /* Cores de Destaque */
  --evo-accent: #FF7A00;         /* Laranja vibrante */
  --evo-accent-light: #FF9500;   /* Laranja claro */
  
  /* Cores Neutras */
  --evo-dark: #0F172A;           /* Azul escuro quase preto */
  --evo-gray: #1E293B;           /* Cinza azulado */
  --evo-light: #F8FAFC;          /* Branco suave */
  --evo-white: #FFFFFF;          /* Branco puro */
  
  /* Cores de Estado */
  --evo-success: #22C55E;        /* Verde sucesso */
  --evo-warning: #F59E0B;        /* Amarelo aviso */
  --evo-danger: #EF4444;         /* Vermelho erro */
  --evo-info: #3B82F6;           /* Azul informação */
}
```

### **Gradientes Tecnológicos**
```css
/* Gradientes Principais */
--evo-gradient-primary: linear-gradient(135deg, #0E8BC0, #30B4E8, #7FD4F7);
--evo-gradient-tech: linear-gradient(135deg, #0F172A, #1E293B);
--evo-gradient-accent: linear-gradient(135deg, #FF7A00, #FF9500);
--evo-gradient-hero: linear-gradient(135deg, #1a1f25 0%, #17263a 100%);

/* Gradientes de Fundo */
--evo-gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
--evo-gradient-overlay: linear-gradient(135deg, rgba(48,180,232,0.1), rgba(30,107,184,0.05));
```

---

## 🔤 **TIPOGRAFIA**

### **Família de Fontes**
```css
/* Fonte Principal */
font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;

/* Fonte Tecnológica (para títulos especiais) */
font-family: 'Orbitron', 'Inter', monospace;
```

### **Escala Tipográfica**
```css
/* Títulos */
.display-1 { font-size: 4rem; font-weight: 800; }    /* Hero principal */
.display-2 { font-size: 3.5rem; font-weight: 800; }  /* Títulos seção */
.display-3 { font-size: 3rem; font-weight: 700; }    /* Subtítulos */
.display-4 { font-size: 2.5rem; font-weight: 700; }  /* Títulos cards */

/* Cabeçalhos */
h1 { font-size: 2.5rem; font-weight: 700; }
h2 { font-size: 2rem; font-weight: 700; }
h3 { font-size: 1.75rem; font-weight: 600; }
h4 { font-size: 1.5rem; font-weight: 600; }
h5 { font-size: 1.25rem; font-weight: 600; }
h6 { font-size: 1rem; font-weight: 600; }

/* Texto */
.lead { font-size: 1.25rem; font-weight: 400; }
.text-lg { font-size: 1.125rem; }
.text-base { font-size: 1rem; }
.text-sm { font-size: 0.875rem; }
.text-xs { font-size: 0.75rem; }
```

---

## 🎯 **COMPONENTES PRINCIPAIS**

### **1. BOTÕES**

#### **Botão Primário**
```css
.btn-evo-primary {
  background: var(--evo-gradient-primary);
  border: none;
  color: white;
  font-weight: 600;
  padding: 12px 24px;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.btn-evo-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 20px rgba(48, 180, 232, 0.6), 0 10px 30px rgba(0, 0, 0, 0.1);
  color: white;
}
```

#### **Botão Secundário**
```css
.btn-outline-evo-primary {
  border: 2px solid var(--evo-primary);
  color: var(--evo-primary);
  background: transparent;
  font-weight: 600;
  padding: 12px 24px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.btn-outline-evo-primary:hover {
  background: var(--evo-primary);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 0 20px rgba(48, 180, 232, 0.6);
}
```

#### **Botão de Destaque**
```css
.btn-evo-accent {
  background: var(--evo-gradient-accent);
  border: none;
  color: white;
  font-weight: 600;
  padding: 12px 24px;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.btn-evo-accent:hover {
  transform: translateY(-2px);
  box-shadow: 0 0 20px rgba(255, 122, 0, 0.6), 0 10px 30px rgba(0, 0, 0, 0.1);
  color: white;
}
```

### **2. CARDS TECNOLÓGICOS**

#### **Card Padrão**
```css
.card-tech {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(48, 180, 232, 0.1);
  border-radius: 20px;
  padding: 2rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.card-tech::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--evo-gradient-primary);
}

.card-tech:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border-color: var(--evo-primary);
}
```

#### **Card Glass (Vidro)**
```css
.card-glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  transition: all 0.3s ease;
}

.card-glass:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(48, 180, 232, 0.5);
  transform: translateY(-5px);
}
```

### **3. FORMULÁRIOS**

#### **Input Padrão**
```css
.form-control {
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.form-control:focus {
  border-color: var(--evo-primary);
  box-shadow: 0 0 0 0.25rem rgba(48, 180, 232, 0.15);
  outline: none;
}
```

#### **Input Floating Label**
```css
.form-floating .form-control {
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.form-floating .form-control:focus {
  border-color: var(--evo-primary);
  box-shadow: 0 0 0 0.25rem rgba(48, 180, 232, 0.15);
}

.form-floating label {
  color: var(--evo-gray);
  font-weight: 500;
}
```

### **4. NAVEGAÇÃO**

#### **Navbar Transparente**
```css
.navbar-transparent {
  background: rgba(15, 23, 42, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.nav-link {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
  transition: all 0.3s ease;
  position: relative;
}

.nav-link:hover {
  color: var(--evo-primary);
  transform: translateY(-2px);
}
```

#### **Sidebar**
```css
.sidebar {
  background: linear-gradient(180deg, #2A9DF4 0%, #1E6BB8 100%);
  min-height: 100vh;
  width: 280px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(42, 157, 244, 0.5);
}

.sidebar-nav .nav-link {
  color: rgba(255, 255, 255, 0.8);
  padding: 1rem 1.5rem;
  border-radius: 12px;
  margin: 0.25rem 1rem;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.sidebar-nav .nav-link:hover,
.sidebar-nav .nav-link.active {
  color: white;
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateX(5px);
}
```

---

## ✨ **ANIMAÇÕES E EFEITOS**

### **Animações Principais**
```css
/* Flutuação Suave */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}
.animate-float { animation: float 6s ease-in-out infinite; }

/* Pulsação com Brilho */
@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 5px var(--evo-primary); }
  50% { box-shadow: 0 0 20px rgba(48, 180, 232, 0.6); }
}
.animate-pulse-glow { animation: pulse-glow 2s ease-in-out infinite; }

/* Rotação Lenta */
@keyframes spin-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
.animate-spin-slow { animation: spin-slow 20s linear infinite; }

/* Entrada Suave */
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(30px); }
  to { opacity: 1; transform: translateY(0); }
}
.animate-fade-in-up { animation: fadeInUp 0.6s ease-out; }

/* Bounce Tecnológico */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
  40%, 43% { transform: translate3d(0,-10px,0); }
  70% { transform: translate3d(0,-5px,0); }
  90% { transform: translate3d(0,-2px,0); }
}
.animate-bounce { animation: bounce 2s infinite; }
```

### **Efeitos de Hover**
```css
/* Efeito Glow */
.glow-effect:hover {
  box-shadow: 0 0 20px rgba(48, 180, 232, 0.6);
  transition: all 0.3s ease;
}

/* Elevação */
.hover-lift:hover {
  transform: translateY(-5px);
  transition: all 0.3s ease;
}

/* Escala */
.hover-scale:hover {
  transform: scale(1.05);
  transition: all 0.3s ease;
}
```

---

## 🎭 **ÍCONES E ELEMENTOS VISUAIS**

### **Ícones Principais**
- **Bootstrap Icons**: Biblioteca principal
- **FontAwesome**: Para ícones especiais
- **SVG Customizados**: Para drones e elementos únicos

### **Drone SVG Animado**
```html
<svg width="200" height="150" viewBox="0 0 200 150" class="drone-float">
  <!-- Corpo do Drone -->
  <rect x="85" y="65" width="30" height="20" rx="4" fill="white" opacity="0.9"/>
  
  <!-- Braços -->
  <rect x="50" y="72" width="35" height="6" rx="3" fill="white" opacity="0.7"/>
  <rect x="115" y="72" width="35" height="6" rx="3" fill="white" opacity="0.7"/>
  
  <!-- Motores -->
  <circle cx="50" cy="75" r="8" fill="white" opacity="0.8"/>
  <circle cx="150" cy="75" r="8" fill="white" opacity="0.8"/>
  
  <!-- Hélices Animadas -->
  <g class="animate-spin-slow">
    <rect x="35" y="73" width="30" height="4" rx="2" fill="white" opacity="0.6"/>
    <rect x="48" y="60" width="4" height="30" rx="2" fill="white" opacity="0.6"/>
  </g>
  
  <!-- LEDs Pulsantes -->
  <circle cx="90" cy="70" r="2" fill="#FF7A00" class="animate-pulse-glow"/>
  <circle cx="110" cy="70" r="2" fill="white" class="animate-pulse-glow"/>
</svg>
```

---

## 📱 **RESPONSIVIDADE**

### **Breakpoints**
```css
/* Mobile First Approach */
/* Extra Small (xs) */
@media (max-width: 575.98px) { }

/* Small (sm) */
@media (min-width: 576px) and (max-width: 767.98px) { }

/* Medium (md) */
@media (min-width: 768px) and (max-width: 991.98px) { }

/* Large (lg) */
@media (min-width: 992px) and (max-width: 1199.98px) { }

/* Extra Large (xl) */
@media (min-width: 1200px) { }
```

### **Adaptações Mobile**
```css
@media (max-width: 768px) {
  /* Tipografia */
  .display-1 { font-size: 2.5rem; }
  .display-2 { font-size: 2rem; }
  
  /* Botões */
  .btn-lg { padding: 0.75rem 1.5rem; font-size: 1rem; }
  
  /* Cards */
  .card-tech { padding: 1.5rem; margin-bottom: 1rem; }
  
  /* Sidebar */
  .sidebar { transform: translateX(-100%); }
  .sidebar.show { transform: translateX(0); }
  .main-content { margin-left: 0; }
}
```

---

## 🌟 **ESTADOS E FEEDBACK**

### **Estados de Loading**
```css
.btn-loading {
  position: relative;
  color: transparent !important;
}

.btn-loading::after {
  content: '';
  position: absolute;
  top: 50%; left: 50%;
  transform: translate(-50%, -50%);
  width: 20px; height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
```

### **Notificações Toast**
```css
.toast-evo {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(48, 180, 232, 0.2);
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.toast-success { border-left: 4px solid var(--evo-success); }
.toast-warning { border-left: 4px solid var(--evo-warning); }
.toast-danger { border-left: 4px solid var(--evo-danger); }
.toast-info { border-left: 4px solid var(--evo-info); }
```

---

## 🎨 **BACKGROUNDS E TEXTURAS**

### **Background Animado**
```css
.page-background {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  z-index: -1;
  background: 
    radial-gradient(circle at 20% 30%, rgba(42, 157, 244, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 70%, rgba(30, 107, 184, 0.15) 0%, transparent 50%);
}

.page-background::before {
  content: '';
  position: absolute;
  width: 200%; height: 200%;
  top: -50%; left: -50%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M10,50 L90,50 M50,10 L50,90" stroke="rgba(42,157,244,0.03)" stroke-width="1"/></svg>') center/30px 30px;
  animation: backgroundMove 60s linear infinite;
}

@keyframes backgroundMove {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
```

---

## 📐 **ESPAÇAMENTOS E GRID**

### **Sistema de Espaçamento**
```css
/* Baseado em múltiplos de 0.25rem (4px) */
.p-1 { padding: 0.25rem; }    /* 4px */
.p-2 { padding: 0.5rem; }     /* 8px */
.p-3 { padding: 1rem; }       /* 16px */
.p-4 { padding: 1.5rem; }     /* 24px */
.p-5 { padding: 3rem; }       /* 48px */

/* Espaçamentos Customizados */
.p-evo-sm { padding: 1rem; }
.p-evo-md { padding: 2rem; }
.p-evo-lg { padding: 3rem; }
.p-evo-xl { padding: 5rem; }
```

### **Grid System**
```css
/* Container Responsivo */
.container-evo {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
}

@media (max-width: 768px) {
  .container-evo { padding: 0 1rem; }
}
```

---

## 🎯 **DIRETRIZES DE USO**

### **Hierarquia Visual**
1. **Primário**: Azul ciano (#30B4E8) - Ações principais, links importantes
2. **Secundário**: Laranja (#FF7A00) - Destaques, CTAs especiais
3. **Neutro**: Cinza azulado (#1E293B) - Texto, elementos secundários

### **Consistência**
- **Sempre** usar border-radius de 12px para botões
- **Sempre** usar border-radius de 16px-20px para cards
- **Sempre** aplicar transições suaves (0.3s ease)
- **Sempre** manter hierarquia tipográfica

### **Acessibilidade**
- Contraste mínimo de 4.5:1 para texto
- Foco visível em todos os elementos interativos
- Suporte a navegação por teclado
- Textos alternativos em imagens

---

## 🚀 **IMPLEMENTAÇÃO**

### **Estrutura de Arquivos CSS**
```
assets/css/
├── main.css           # Design system principal
├── global.css         # Estilos globais
├── components/        # Componentes específicos
│   ├── buttons.css
│   ├── cards.css
│   ├── forms.css
│   └── navigation.css
└── animations.css     # Animações e efeitos
```

### **Ordem de Carregamento**
1. Bootstrap 5.3.3
2. Google Fonts (Inter)
3. Bootstrap Icons
4. main.css (Design System)
5. global.css
6. Componentes específicos

---

---

## 🎭 **MODAIS E OVERLAYS**

### **Modal Padrão**
```css
.modal-evo {
  backdrop-filter: blur(10px);
}

.modal-evo .modal-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(48, 180, 232, 0.1);
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.modal-evo .modal-header {
  border-bottom: 1px solid rgba(48, 180, 232, 0.1);
  padding: 1.5rem 2rem;
}

.modal-evo .modal-body {
  padding: 2rem;
}

.modal-evo .modal-footer {
  border-top: 1px solid rgba(48, 180, 232, 0.1);
  padding: 1.5rem 2rem;
}
```

### **Overlay de Loading**
```css
.loading-overlay {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(15, 23, 42, 0.8);
  backdrop-filter: blur(5px);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(48, 180, 232, 0.3);
  border-top: 4px solid var(--evo-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
```

---

## 📊 **TABELAS E DADOS**

### **Tabela Responsiva**
```css
.table-evo {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.table-evo thead {
  background: var(--evo-gradient-primary);
  color: white;
}

.table-evo th {
  font-weight: 600;
  padding: 1rem 1.5rem;
  border: none;
}

.table-evo td {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(48, 180, 232, 0.1);
  vertical-align: middle;
}

.table-evo tbody tr:hover {
  background: rgba(48, 180, 232, 0.05);
  transition: all 0.3s ease;
}
```

### **Cards de Estatísticas**
```css
.stats-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stats-card:hover {
  transform: translateY(-10px);
  border-color: rgba(48, 180, 232, 0.5);
  box-shadow: 0 10px 30px rgba(48, 180, 232, 0.2);
}

.stats-number {
  font-size: 3.5rem;
  font-weight: 700;
  background: var(--evo-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 0.5rem;
  line-height: 1;
}

.stats-title {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.25rem;
  font-weight: 500;
  margin: 0;
}
```

---

## 🎨 **BADGES E LABELS**

### **Badges de Status**
```css
.badge-evo {
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.badge-success {
  background: rgba(34, 197, 94, 0.1);
  color: #059669;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.badge-warning {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.badge-danger {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.badge-info {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.badge-primary {
  background: rgba(48, 180, 232, 0.1);
  color: var(--evo-primary-dark);
  border: 1px solid rgba(48, 180, 232, 0.2);
}
```

---

## 🔧 **UTILITÁRIOS AVANÇADOS**

### **Glassmorphism**
```css
.glass-light {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(15, 23, 42, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-primary {
  background: rgba(48, 180, 232, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(48, 180, 232, 0.2);
}
```

### **Efeitos de Texto**
```css
.text-glow {
  text-shadow: 0 0 10px rgba(48, 180, 232, 0.5);
}

.text-gradient-primary {
  background: var(--evo-gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.text-gradient-accent {
  background: var(--evo-gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
```

### **Bordas Especiais**
```css
.border-glow {
  border: 1px solid var(--evo-primary);
  box-shadow: 0 0 10px rgba(48, 180, 232, 0.3);
}

.border-gradient {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              var(--evo-gradient-primary) border-box;
}
```

---

## 📱 **COMPONENTES MOBILE**

### **Bottom Navigation**
```css
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(48, 180, 232, 0.1);
  padding: 1rem 0;
  z-index: 1000;
}

.bottom-nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-decoration: none;
  color: var(--evo-gray);
  transition: all 0.3s ease;
  padding: 0.5rem;
}

.bottom-nav-item.active,
.bottom-nav-item:hover {
  color: var(--evo-primary);
  transform: translateY(-2px);
}

.bottom-nav-icon {
  font-size: 1.5rem;
  margin-bottom: 0.25rem;
}

.bottom-nav-label {
  font-size: 0.75rem;
  font-weight: 500;
}
```

### **Swipe Cards**
```css
.swipe-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 1.5rem;
  margin: 1rem 0;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: grab;
}

.swipe-card:active {
  cursor: grabbing;
  transform: scale(0.98);
}
```

---

## 🎯 **MICROINTERAÇÕES**

### **Ripple Effect**
```css
.ripple {
  position: relative;
  overflow: hidden;
}

.ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
  width: 300px;
  height: 300px;
}
```

### **Magnetic Hover**
```css
.magnetic {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.magnetic:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}
```

---

## 🎨 **TEMAS E VARIAÇÕES**

### **Modo Escuro (Futuro)**
```css
[data-theme="dark"] {
  --evo-primary: #7FD4F7;
  --evo-dark: #F8FAFC;
  --evo-light: #0F172A;
  --evo-gray: #E2E8F0;
}

[data-theme="dark"] .card-tech {
  background: rgba(15, 23, 42, 0.95);
  border-color: rgba(127, 212, 247, 0.2);
}

[data-theme="dark"] .text-evo-dark {
  color: var(--evo-light) !important;
}
```

### **Tema Alto Contraste**
```css
[data-theme="high-contrast"] {
  --evo-primary: #0066CC;
  --evo-accent: #FF6600;
  --evo-dark: #000000;
  --evo-light: #FFFFFF;
}

[data-theme="high-contrast"] .btn-evo-primary {
  background: var(--evo-primary);
  border: 2px solid var(--evo-dark);
}
```

---

## 📋 **CHECKLIST DE IMPLEMENTAÇÃO**

### **Para Cada Nova Página:**
- [ ] Aplicar background animado padrão
- [ ] Usar tipografia da escala definida
- [ ] Implementar cards com glassmorphism
- [ ] Adicionar animações de entrada (AOS)
- [ ] Garantir responsividade mobile
- [ ] Aplicar estados de hover/focus
- [ ] Validar contraste de cores
- [ ] Testar navegação por teclado

### **Para Cada Componente:**
- [ ] Seguir padrão de border-radius
- [ ] Aplicar transições suaves
- [ ] Usar variáveis CSS definidas
- [ ] Implementar estados visuais
- [ ] Adicionar microinterações
- [ ] Garantir acessibilidade
- [ ] Testar em diferentes dispositivos

---

## 🚀 **PERFORMANCE E OTIMIZAÇÃO**

### **CSS Crítico**
```css
/* Carregar primeiro - Above the fold */
:root { /* variáveis */ }
body { /* estilos base */ }
.btn-evo-primary { /* botões principais */ }
.card-tech { /* cards principais */ }
```

### **Lazy Loading de Animações**
```css
/* Carregar apenas quando necessário */
.animate-complex {
  animation: none;
}

.animate-complex.in-view {
  animation: complexAnimation 2s ease-out;
}
```

### **Otimizações**
- Usar `transform` e `opacity` para animações
- Evitar animações em propriedades que causam reflow
- Implementar `will-change` para elementos animados
- Usar `contain: layout style paint` quando apropriado

---

**🎯 Este design system completo garante consistência visual excepcional, experiência de usuário superior e facilita a manutenção e expansão da plataforma EVO DRONES com padrões profissionais de desenvolvimento frontend.**
