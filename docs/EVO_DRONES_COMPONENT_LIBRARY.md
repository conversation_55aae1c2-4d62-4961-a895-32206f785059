# 🧩 EVO DRONES - BIBLIOTECA DE COMPONENTES

## 📋 **VISÃO GERAL**

Esta biblioteca contém exemplos práticos de implementação dos componentes do Design System EVO DRONES, com código HTML/CSS pronto para uso.

---

## 🎯 **COMPONENTES PRONTOS**

### **1. HERO SECTION PADRÃO**

```html
<section class="hero-evo">
  <div class="page-background"></div>
  <div class="container-evo">
    <div class="row align-items-center min-vh-100">
      <div class="col-lg-6" data-aos="fade-right">
        <h1 class="display-2 text-gradient-primary mb-4">
          Bem-vindo ao <PERSON>turo
        </h1>
        <p class="lead text-evo-gray mb-5">
          Tecnologia de ponta para manutenção especializada e serviços para drones.
        </p>
        <div class="d-flex gap-3 flex-wrap">
          <button class="btn btn-evo-primary btn-lg glow-effect">
            <i class="bi bi-rocket-takeoff me-2"></i>
            Começar Agora
          </button>
          <button class="btn btn-outline-evo-primary btn-lg">
            <i class="bi bi-play-circle me-2"></i>
            Ver Demo
          </button>
        </div>
      </div>
      <div class="col-lg-6" data-aos="fade-left">
        <div class="drone-container">
          <!-- SVG Drone Animado -->
          <svg class="drone-float" width="400" height="300" viewBox="0 0 400 300">
            <!-- Implementação do drone SVG -->
          </svg>
        </div>
      </div>
    </div>
  </div>
</section>

<style>
.hero-evo {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
}

.drone-container {
  position: relative;
  text-align: center;
}
</style>
```

### **2. CARD DE SERVIÇO**

```html
<div class="service-card" data-aos="fade-up">
  <div class="service-icon">
    <i class="bi bi-tools"></i>
  </div>
  <h4 class="service-title">Manutenção Preventiva</h4>
  <p class="service-description">
    Mantenha seu drone sempre em perfeito estado com nossa manutenção especializada.
  </p>
  <div class="service-features">
    <span class="feature-badge">
      <i class="bi bi-check-circle me-1"></i>
      Diagnóstico Completo
    </span>
    <span class="feature-badge">
      <i class="bi bi-check-circle me-1"></i>
      Peças Originais
    </span>
  </div>
  <button class="btn btn-evo-primary w-100 mt-4">
    Solicitar Serviço
  </button>
</div>

<style>
.service-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(48, 180, 232, 0.1);
  border-radius: 20px;
  padding: 2rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--evo-gradient-primary);
}

.service-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border-color: var(--evo-primary);
}

.service-icon {
  width: 80px;
  height: 80px;
  background: var(--evo-gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 2rem;
  color: white;
  transition: all 0.3s ease;
}

.service-card:hover .service-icon {
  transform: scale(1.1) rotate(360deg);
}

.service-title {
  color: var(--evo-dark);
  font-weight: 700;
  margin-bottom: 1rem;
}

.service-description {
  color: var(--evo-gray);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.service-features {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.feature-badge {
  background: rgba(48, 180, 232, 0.1);
  color: var(--evo-primary-dark);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-size: 0.875rem;
  font-weight: 500;
}
</style>
```

### **3. FORMULÁRIO DE CONTATO**

```html
<div class="contact-form-container">
  <div class="card-glass p-5">
    <h3 class="text-center mb-4 text-gradient-primary">Entre em Contato</h3>
    
    <form class="contact-form" id="contactForm">
      <div class="row g-4">
        <div class="col-md-6">
          <div class="form-floating">
            <input type="text" class="form-control" id="nome" placeholder="Seu nome" required>
            <label for="nome">
              <i class="bi bi-person me-2"></i>Nome Completo
            </label>
          </div>
        </div>
        
        <div class="col-md-6">
          <div class="form-floating">
            <input type="email" class="form-control" id="email" placeholder="<EMAIL>" required>
            <label for="email">
              <i class="bi bi-envelope me-2"></i>Email
            </label>
          </div>
        </div>
        
        <div class="col-12">
          <div class="form-floating">
            <input type="tel" class="form-control" id="telefone" placeholder="(00) 90000-0000" required>
            <label for="telefone">
              <i class="bi bi-phone me-2"></i>Telefone
            </label>
          </div>
        </div>
        
        <div class="col-12">
          <div class="form-floating">
            <textarea class="form-control" id="mensagem" placeholder="Sua mensagem" style="height: 120px" required></textarea>
            <label for="mensagem">
              <i class="bi bi-chat-text me-2"></i>Mensagem
            </label>
          </div>
        </div>
        
        <div class="col-12">
          <button type="submit" class="btn btn-evo-primary w-100 py-3 glow-effect">
            <i class="bi bi-send me-2"></i>
            Enviar Mensagem
          </button>
        </div>
      </div>
    </form>
  </div>
</div>

<style>
.contact-form-container {
  max-width: 600px;
  margin: 0 auto;
}

.contact-form .form-control {
  border: 2px solid rgba(48, 180, 232, 0.1);
  border-radius: 12px;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
}

.contact-form .form-control:focus {
  border-color: var(--evo-primary);
  box-shadow: 0 0 0 0.25rem rgba(48, 180, 232, 0.15);
  background: white;
}

.contact-form .form-floating label {
  color: var(--evo-gray);
  font-weight: 500;
  padding-left: 1.5rem;
}

.contact-form .form-floating .form-control:focus ~ label,
.contact-form .form-floating .form-control:not(:placeholder-shown) ~ label {
  color: var(--evo-primary);
}
</style>
```

### **4. DASHBOARD STATS CARDS**

```html
<div class="stats-grid">
  <div class="stats-card" data-aos="fade-up" data-aos-delay="100">
    <div class="stats-icon">
      <i class="bi bi-drone"></i>
    </div>
    <div class="stats-content">
      <div class="stats-number">
        <span class="counter" data-target="24">0</span>
      </div>
      <h5 class="stats-title">Drones Ativos</h5>
      <div class="stats-change positive">
        <i class="bi bi-arrow-up"></i>
        +12% este mês
      </div>
    </div>
  </div>
  
  <div class="stats-card" data-aos="fade-up" data-aos-delay="200">
    <div class="stats-icon accent">
      <i class="bi bi-tools"></i>
    </div>
    <div class="stats-content">
      <div class="stats-number">
        <span class="counter" data-target="156">0</span>
      </div>
      <h5 class="stats-title">Serviços Realizados</h5>
      <div class="stats-change positive">
        <i class="bi bi-arrow-up"></i>
        +8% este mês
      </div>
    </div>
  </div>
  
  <div class="stats-card" data-aos="fade-up" data-aos-delay="300">
    <div class="stats-icon success">
      <i class="bi bi-check-circle"></i>
    </div>
    <div class="stats-content">
      <div class="stats-number">
        <span class="counter" data-target="98">0</span>%
      </div>
      <h5 class="stats-title">Taxa de Sucesso</h5>
      <div class="stats-change positive">
        <i class="bi bi-arrow-up"></i>
        +2% este mês
      </div>
    </div>
  </div>
</div>

<style>
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
  margin: 2rem 0;
}

.stats-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(48, 180, 232, 0.1);
  border-radius: 20px;
  padding: 2rem;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--evo-gradient-primary);
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
}

.stats-icon {
  width: 60px;
  height: 60px;
  background: var(--evo-gradient-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  margin-bottom: 1.5rem;
  transition: all 0.3s ease;
}

.stats-icon.accent {
  background: var(--evo-gradient-accent);
}

.stats-icon.success {
  background: linear-gradient(135deg, #22C55E, #16A34A);
}

.stats-card:hover .stats-icon {
  transform: scale(1.1);
}

.stats-number {
  font-size: 3rem;
  font-weight: 700;
  color: var(--evo-dark);
  margin-bottom: 0.5rem;
  line-height: 1;
}

.stats-title {
  color: var(--evo-gray);
  font-weight: 600;
  margin-bottom: 1rem;
}

.stats-change {
  font-size: 0.875rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.stats-change.positive {
  color: var(--evo-success);
}

.stats-change.negative {
  color: var(--evo-danger);
}
</style>

<script>
// Animação de contador
function animateCounters() {
  const counters = document.querySelectorAll('.counter');
  
  counters.forEach(counter => {
    const target = parseInt(counter.getAttribute('data-target'));
    const duration = 2000;
    const step = target / (duration / 16);
    let current = 0;
    
    const timer = setInterval(() => {
      current += step;
      if (current >= target) {
        counter.textContent = target;
        clearInterval(timer);
      } else {
        counter.textContent = Math.floor(current);
      }
    }, 16);
  });
}

// Executar quando os elementos estiverem visíveis
const observer = new IntersectionObserver((entries) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      animateCounters();
      observer.unobserve(entry.target);
    }
  });
});

document.querySelectorAll('.stats-card').forEach(card => {
  observer.observe(card);
});
</script>
```

### **5. NAVEGAÇÃO BREADCRUMB**

```html
<nav class="breadcrumb-evo" aria-label="breadcrumb">
  <ol class="breadcrumb">
    <li class="breadcrumb-item">
      <a href="/dashboard">
        <i class="bi bi-house-door"></i>
        Dashboard
      </a>
    </li>
    <li class="breadcrumb-item">
      <a href="/drones">
        <i class="bi bi-drone"></i>
        Drones
      </a>
    </li>
    <li class="breadcrumb-item active" aria-current="page">
      <i class="bi bi-plus-circle"></i>
      Cadastrar Novo
    </li>
  </ol>
</nav>

<style>
.breadcrumb-evo {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1rem 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid rgba(48, 180, 232, 0.1);
}

.breadcrumb-evo .breadcrumb {
  margin: 0;
  background: none;
  padding: 0;
}

.breadcrumb-evo .breadcrumb-item {
  font-weight: 500;
}

.breadcrumb-evo .breadcrumb-item a {
  color: var(--evo-primary);
  text-decoration: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.breadcrumb-evo .breadcrumb-item a:hover {
  color: var(--evo-primary-dark);
  transform: translateX(2px);
}

.breadcrumb-evo .breadcrumb-item.active {
  color: var(--evo-gray);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.breadcrumb-evo .breadcrumb-item + .breadcrumb-item::before {
  content: ">";
  color: var(--evo-gray);
  margin: 0 0.5rem;
}
</style>
```

---

## 🎨 **TEMPLATES DE PÁGINA**

### **Template Dashboard**

```html
<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Dashboard - EVO DRONES</title>
  
  <!-- CSS Framework -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
  <link href="https://unpkg.com/aos@2.3.4/dist/aos.css" rel="stylesheet">
  
  <!-- EVO DRONES CSS -->
  <link rel="stylesheet" href="/assets/css/main.css">
  <link rel="stylesheet" href="/assets/css/global.css">
</head>
<body>
  <!-- Background Animado -->
  <div class="page-background"></div>
  
  <!-- Sidebar -->
  <nav class="sidebar" id="sidebar">
    <!-- Conteúdo da sidebar -->
  </nav>
  
  <!-- Conteúdo Principal -->
  <div class="main-content">
    <!-- Header -->
    <header class="navbar-top">
      <!-- Conteúdo do header -->
    </header>
    
    <!-- Breadcrumb -->
    <div class="container-fluid p-4">
      <!-- Breadcrumb component -->
    </div>
    
    <!-- Conteúdo da Página -->
    <main class="container-fluid p-4">
      <!-- Stats Cards -->
      <div class="stats-grid mb-5">
        <!-- Stats components -->
      </div>
      
      <!-- Conteúdo Principal -->
      <div class="row g-4">
        <!-- Cards e componentes -->
      </div>
    </main>
  </div>
  
  <!-- Scripts -->
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://unpkg.com/aos@2.3.4/dist/aos.js"></script>
  <script src="/assets/js/main.js"></script>
  
  <script>
    // Inicializar AOS
    AOS.init({
      duration: 600,
      once: true
    });
  </script>
</body>
</html>
```

---

## 🚀 **JAVASCRIPT UTILITÁRIOS**

### **Classe Principal EVO DRONES**

```javascript
class EvoDrones {
  // Toast notifications
  static showToast(message, type = 'info', duration = 5000) {
    const toastContainer = document.getElementById('toast-container') || this.createToastContainer();
    
    const toast = document.createElement('div');
    toast.className = `toast-evo toast-${type} show`;
    toast.innerHTML = `
      <div class="d-flex align-items-center">
        <div class="toast-icon me-3">
          <i class="bi bi-${this.getToastIcon(type)}"></i>
        </div>
        <div class="flex-grow-1">
          ${message}
        </div>
        <button type="button" class="btn-close" onclick="this.parentElement.parentElement.remove()"></button>
      </div>
    `;
    
    toastContainer.appendChild(toast);
    
    setTimeout(() => {
      toast.remove();
    }, duration);
  }
  
  // Formatação de moeda
  static formatCurrency(value) {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  }
  
  // Formatação de data
  static formatDate(date) {
    return new Intl.DateTimeFormat('pt-BR').format(new Date(date));
  }
  
  // Debounce para otimização
  static debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }
  
  // Utilitários privados
  static createToastContainer() {
    const container = document.createElement('div');
    container.id = 'toast-container';
    container.className = 'position-fixed top-0 end-0 p-3';
    container.style.zIndex = '9999';
    document.body.appendChild(container);
    return container;
  }
  
  static getToastIcon(type) {
    const icons = {
      success: 'check-circle',
      danger: 'exclamation-triangle',
      warning: 'exclamation-circle',
      info: 'info-circle'
    };
    return icons[type] || 'info-circle';
  }
}

// Inicialização global
document.addEventListener('DOMContentLoaded', function() {
  // Inicializar tooltips
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.map(function (tooltipTriggerEl) {
    return new bootstrap.Tooltip(tooltipTriggerEl);
  });
  
  // Inicializar popovers
  const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
  popoverTriggerList.map(function (popoverTriggerEl) {
    return new bootstrap.Popover(popoverTriggerEl);
  });
});
```

---

**🎯 Esta biblioteca de componentes fornece implementações práticas e prontas para uso, garantindo consistência e agilidade no desenvolvimento da plataforma EVO DRONES.**
